from manim import *
import numpy as np

class FibonacciOrganicIllusion(MovingCameraScene):
    def construct(self):
        total_points = 2000
        golden_angle = 2 * np.pi * (1 - 1 / ((1 + 5 ** 0.5) / 2))
        petal_skip = 34

        dots = VGroup()
        petals = VGroup()
        glow_circles = VGroup()

        # Step 1: Spiral Dots
        for i in range(total_points):
            theta = i * golden_angle
            r = np.sqrt(i) * 0.06
            x = r * np.cos(theta)
            y = r * np.sin(theta)
            dot = Dot(point=[x, y, 0], radius=0.01, color=WHITE)
            dots.add(dot)

        self.play(
            LaggedStart(*[FadeIn(dot, scale=0.5) for dot in dots], lag_ratio=0.001),
            run_time=2
        )
        self.wait(0.3)

        # Step 2: Colorful Petal Connections
        for i in range(total_points - petal_skip):
            p1 = dots[i].get_center()
            p2 = dots[i + petal_skip].get_center()
            color = color_gradient([RED, ORANGE, YELLOW, GREEN, BLUE, PURPLE], total_points)[i % total_points]
            line = Line(p1, p2, color=color, stroke_width=1, stroke_opacity=0.6)
            petals.add(line)

        self.play(
            LaggedStart(*[Create(line) for line in petals], lag_ratio=0.001),
            run_time=4
        )
        self.wait(0.5)

        # Step 3: Add subtle glowing rings (illusion of structure)
        for radius in np.linspace(0.2, 2.8, 6):
            ring = Circle(radius=radius, color=WHITE, stroke_opacity=0.15, stroke_width=2)
            glow_circles.add(ring)

        glow_circles.move_to(dots[0].get_center())
        self.play(FadeIn(glow_circles), run_time=2)
        self.wait(0.5)

        # Step 4: Zoom in beautifully
        self.play(
            self.camera.frame.animate.scale(0.7).move_to(dots[0].get_center()),
            run_time=3,
            rate_func=smooth
        )
        self.wait(3)
