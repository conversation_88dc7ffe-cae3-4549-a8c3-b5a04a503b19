from manim import *
import numpy as np

class FibonacciOrganicIllusion(MovingCameraScene):
    def construct(self):
        total_points = 2000
        golden_angle = 2 * np.pi * (1 - 1 / ((1 + 5 ** 0.5) / 2))
        petal_skip = 34

        dots = VGroup()
        petals = VGroup()
        center_seeds = VGroup()

        # Realistic sunflower colors
        petal_colors = [
            "#FFD700",  # Deep golden yellow
            "#FFA500",  # Orange
            "#FF8C00",  # Dark orange
            "#FFFF00",  # Bright yellow
            "#F0E68C",  # Khaki
        ]

        center_colors = [
            "#8B4513",  # Saddle brown
            "#A0522D",  # Sienna
            "#CD853F",  # Peru
            "#D2691E",  # Chocolate
        ]

        # Step 1: Create spiral dots (seeds in center)
        for i in range(total_points):
            theta = i * golden_angle
            r = np.sqrt(i) * 0.06
            x = r * np.cos(theta)
            y = r * np.sin(theta)

            # Use brown colors for center seeds
            if r < 0.8:
                color = center_colors[i % len(center_colors)]
                radius = 0.015
            else:
                color = "#2F4F2F"  # Dark green for outer area
                radius = 0.008

            dot = Dot(point=[x, y, 0], radius=radius, color=color)
            dots.add(dot)

        self.play(
            LaggedStart(*[FadeIn(dot, scale=0.5) for dot in dots], lag_ratio=0.001),
            run_time=2
        )
        self.wait(0.3)

        # Step 2: Create realistic petal shapes
        def create_petal(start_point, end_point, color, width_factor=1.0):
            # Calculate petal direction and length
            direction = end_point - start_point
            length = np.linalg.norm(direction)
            if length == 0:
                return VGroup()

            direction_normalized = direction / length
            perpendicular = np.array([-direction_normalized[1], direction_normalized[0], 0])

            # Create petal shape with multiple points for realistic curve
            petal_width = length * 0.15 * width_factor

            # Define petal outline points
            points = []
            # Start narrow at base
            points.append(start_point)
            # Widen towards middle
            mid_point = start_point + direction * 0.4
            points.append(mid_point + perpendicular * petal_width * 0.3)
            points.append(mid_point + direction_normalized * length * 0.2 + perpendicular * petal_width * 0.8)
            # Peak of petal
            peak = start_point + direction * 0.8
            points.append(peak + perpendicular * petal_width * 0.4)
            points.append(end_point)  # Tip of petal
            points.append(peak - perpendicular * petal_width * 0.4)
            # Other side
            points.append(mid_point + direction_normalized * length * 0.2 - perpendicular * petal_width * 0.8)
            points.append(mid_point - perpendicular * petal_width * 0.3)
            points.append(start_point)

            # Create filled polygon for petal
            petal = Polygon(*points, fill_opacity=0.8, stroke_opacity=0.9,
                          fill_color=color, stroke_color=color, stroke_width=1)
            return petal

        # Create petals connecting spiral points
        for i in range(total_points - petal_skip):
            p1 = dots[i].get_center()
            p2 = dots[i + petal_skip].get_center()

            # Only create petals for outer points (actual flower petals)
            r1 = np.linalg.norm(p1[:2])
            if r1 > 0.8:  # Only outer points become petals
                color = petal_colors[i % len(petal_colors)]
                petal = create_petal(p1, p2, color, width_factor=1.2)
                if len(petal) > 0:
                    petals.add(petal)

        self.play(
            LaggedStart(*[FadeIn(petal) for petal in petals], lag_ratio=0.002),
            run_time=4
        )
        self.wait(0.5)

        # Step 3: Add center seed detail
        center_radius = 0.8
        for i in range(500):
            theta = i * golden_angle * 0.5
            r = np.sqrt(i) * 0.02
            if r < center_radius:
                x = r * np.cos(theta)
                y = r * np.sin(theta)
                seed_color = center_colors[i % len(center_colors)]
                seed = Circle(radius=0.008, color=seed_color, fill_opacity=0.9,
                            stroke_opacity=0.7).move_to([x, y, 0])
                center_seeds.add(seed)

        self.play(FadeIn(center_seeds), run_time=2)
        self.wait(0.5)

        # Step 4: Zoom in beautifully
        self.play(
            self.camera.frame.animate.scale(0.7).move_to(ORIGIN),
            run_time=3,
            rate_func=smooth
        )
        self.wait(3)
