from manim import *
import numpy as np

class RealisticSunflowerGrowth(MovingCameraScene):
    def construct(self):
        # Fibonacci parameters for realistic sunflower
        total_seeds = 1500
        total_petals = 55  # Typical sunflower petal count
        golden_angle = 2 * np.pi * (1 - 1 / ((1 + 5 ** 0.5) / 2))

        # Groups for different parts
        seed_dots = VGroup()
        seed_spirals = VGroup()
        petal_group = VGroup()
        petal_outlines = VGroup()

        # Photorealistic sunflower colors with gradients
        center_colors = [
            "#2F1B14",  # Dark brown center
            "#4A2C17",  # Medium brown
            "#6B3E07",  # Darker golden brown
            "#8B4513",  # Saddle brown
            "#A0522D",  # Sienna
            "#CD853F",  # Peru
        ]

        petal_base_colors = [
            "#B8860B",  # Dark golden rod (base)
            "#DAA520",  # Golden rod
            "#FFD700",  # Gold
            "#FFA500",  # Orange
            "#FF8C00",  # Dark orange
        ]

        petal_tip_colors = [
            "#FFFF99",  # Light yellow
            "#FFFFE0",  # Light yellow
            "#FFF8DC",  # Cornsilk
            "#FFFACD",  # Lemon chiffon
        ]

        # Step 1: Create Fibonacci spiral seed pattern
        for i in range(total_seeds):
            theta = i * golden_angle
            r = np.sqrt(i) * 0.04  # Tighter spiral for realistic seed density
            x = r * np.cos(theta)
            y = r * np.sin(theta)

            # Create realistic seed appearance with size variation
            if r < 1.2:  # Center seeds
                color = center_colors[i % len(center_colors)]
                radius = 0.012 + 0.004 * np.sin(i * 0.1)  # Slight size variation
                seed = Circle(radius=radius, color=color, fill_opacity=0.95,
                            stroke_color=center_colors[(i+2) % len(center_colors)],
                            stroke_width=0.5, stroke_opacity=0.8)
                seed.move_to([x, y, 0])
                seed_dots.add(seed)

        # Animate seed formation
        self.play(
            LaggedStart(*[FadeIn(seed, scale=0.3) for seed in seed_dots], lag_ratio=0.0008),
            run_time=3
        )
        self.wait(0.5)

        # Step 2: Create realistic sunflower petals with proper Fibonacci arrangement
        def create_realistic_petal(angle, length, petal_index):
            # Calculate petal position radiating from center
            base_x = 1.3 * np.cos(angle)  # Start petals outside seed area
            base_y = 1.3 * np.sin(angle)
            tip_x = (1.3 + length) * np.cos(angle)
            tip_y = (1.3 + length) * np.sin(angle)

            # Create gradient from base to tip
            base_color = petal_base_colors[petal_index % len(petal_base_colors)]
            tip_color = petal_tip_colors[petal_index % len(petal_tip_colors)]

            # Create realistic petal shape with natural curves
            petal_width = length * 0.25

            # Define petal control points for natural shape
            base_point = np.array([base_x, base_y, 0])
            tip_point = np.array([tip_x, tip_y, 0])

            # Perpendicular vector for width
            perp = np.array([-np.sin(angle), np.cos(angle), 0])

            # Create petal outline with natural curves
            points = []

            # Left side of petal (going from base to tip)
            for t in np.linspace(0, 1, 8):
                # Natural petal curve - narrow at base, wide in middle, pointed at tip
                width_factor = 4 * t * (1 - t)  # Parabolic width distribution
                current_width = petal_width * width_factor

                # Interpolate position along petal length
                pos = base_point + t * (tip_point - base_point)
                # Add width offset
                points.append(pos + current_width * perp)

            # Right side of petal (going from tip to base)
            for t in np.linspace(1, 0, 8):
                width_factor = 4 * t * (1 - t)
                current_width = petal_width * width_factor
                pos = base_point + t * (tip_point - base_point)
                points.append(pos - current_width * perp)

            # Create petal with gradient effect
            petal = Polygon(*points, fill_opacity=0.9, stroke_opacity=0.7,
                          fill_color=base_color, stroke_color=tip_color, stroke_width=1.5)

            # Add subtle texture lines
            center_line = Line(base_point, tip_point, color=tip_color,
                             stroke_width=0.8, stroke_opacity=0.6)

            petal_with_texture = VGroup(petal, center_line)
            return petal_with_texture

        # Create petals using Fibonacci angles for natural arrangement
        for i in range(total_petals):
            # Use Fibonacci angle for natural petal distribution
            petal_angle = i * golden_angle
            # Vary petal length slightly for natural look
            petal_length = 1.8 + 0.3 * np.sin(i * 0.7)  # Natural length variation

            petal = create_realistic_petal(petal_angle, petal_length, i)
            petal_group.add(petal)

        # Animate petal growth
        self.play(
            LaggedStart(*[GrowFromCenter(petal) for petal in petal_group], lag_ratio=0.05),
            run_time=5
        )
        self.wait(0.5)

        # Step 3: Show Fibonacci spiral connections in seeds
        spiral_lines = VGroup()
        fibonacci_numbers = [1, 1, 2, 3, 5, 8, 13, 21, 34, 55]

        for fib_num in fibonacci_numbers[:6]:  # Show first few Fibonacci spirals
            for i in range(0, len(seed_dots) - fib_num, 3):  # Skip some for clarity
                if i + fib_num < len(seed_dots):
                    start_pos = seed_dots[i].get_center()
                    end_pos = seed_dots[i + fib_num].get_center()

                    # Only show connections in center area
                    if np.linalg.norm(start_pos[:2]) < 1.0:
                        spiral_line = Line(start_pos, end_pos,
                                         color=center_colors[fib_num % len(center_colors)],
                                         stroke_width=0.8, stroke_opacity=0.4)
                        spiral_lines.add(spiral_line)

        self.play(
            LaggedStart(*[Create(line) for line in spiral_lines], lag_ratio=0.01),
            run_time=3
        )
        self.wait(1)

        # Step 4: Add petal outlines to show structure
        for petal in petal_group:
            outline = petal.copy()
            outline.set_fill(opacity=0)
            outline.set_stroke(color=WHITE, width=2, opacity=0.3)
            petal_outlines.add(outline)

        self.play(FadeIn(petal_outlines), run_time=1.5)
        self.wait(0.5)

        # Step 5: Final dramatic zoom and rotation
        self.play(
            self.camera.frame.animate.scale(0.6).move_to(ORIGIN),
            Rotate(VGroup(seed_dots, petal_group, spiral_lines), angle=PI/6, about_point=ORIGIN),
            run_time=4,
            rate_func=smooth
        )
        self.wait(2)
